#!/usr/bin/env python3
"""
基于已提取的 UQ 结果 CSV (无需重新编码原始 responses) 对 sentiment_analysis / topic_labeling 任务做如下分析：
1. 直接利用 Embedding 方法 (EmbeddingE5UQ / EmbeddingQwenUQ) 在 full_result 中的 avg_distance_to_reference (responses 与 reference 的均值距离) 与 avg_candidate_distance (responses 两两平均距离 / pairwise) 作为 X 与基础 Y。
2. 支持用多个指定 UQ 方法 (列表) 的 uq_score (uncertainty_score 或 uq_value) 作为多组 Y，与 X (reference 距离) 共同绘制散点并分别线性拟合。
3. 生成：
    - 主 CSV (聚合每个 document_id: reference_distance, pairwise_distance, 各 UQ 方法分数)
    - 单个或多方法散点图 (颜色区分方法) + 拟合直线
    - 拟合参数 JSON 摘要

数据来源: uq_result_analysis/data/UQ_result_{task}.csv (由 data_extractor 生成)。该 CSV 每行 = document_id + uq_method。需 pivot 成 document 粒度。

依赖: pandas, numpy, matplotlib, (可选) scikit-learn；若无则使用 numpy 线性回归。

示例:
python embedding_reference_scatter.py --task sentiment_analysis --uq-methods EmbeddingE5UQ,LUQUQ,NumSetsUQ
python embedding_reference_scatter.py --task topic_labeling --uq-methods EmbeddingQwenUQ,E5

注意: 若某 document 缺少 avg_distance_to_reference (reference 不存在或未设置) 将被过滤。

"""
from __future__ import annotations
import os, argparse, json, math
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import pandas as pd
import numpy as np
from tqdm import tqdm

try:
    from sklearn.linear_model import LinearRegression
    SKLEARN_AVAILABLE = True
except Exception:
    SKLEARN_AVAILABLE = False

# 项目内部依赖
 # 不再需要运行时重新编码 responses, 直接使用 CSV 中 embedding 方法保存的统计指标

DATA_DIR = Path('uq_result_analysis/data')
FIG_DIR = Path('uq_result_analysis/figures')
FIG_DIR.mkdir(parents=True, exist_ok=True)

REFERENCE_TASKS = {"sentiment_analysis", "topic_labeling"}

def safe_float(v):
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

def load_task_dataframe(task: str) -> pd.DataFrame:
    # 优先使用带reference的文件
    csv_path_with_ref = DATA_DIR / f"UQ_result_{task}_with_reference.csv"
    csv_path = DATA_DIR / f"UQ_result_{task}.csv"

    if csv_path_with_ref.exists():
        df = pd.read_csv(csv_path_with_ref)
        print(f"使用带reference距离的数据文件: {csv_path_with_ref}")
    elif csv_path.exists():
        df = pd.read_csv(csv_path)
        print(f"使用原始数据文件: {csv_path}")
    else:
        raise FileNotFoundError(f"未找到数据文件: {csv_path} 或 {csv_path_with_ref}")

    return df

def prepare_grouped(df: pd.DataFrame) -> pd.DataFrame:
    # 每个 document_id + uq_method 是一行; 需要聚合为每个 input_text 的多响应集合
    # 假设同一 document_id 代表一个 input_text + 多 responses
    # 选择我们需要的列
    return df

def extract_embedding_distances(df: pd.DataFrame) -> pd.DataFrame:
    """过滤 embedding 相关方法行，提取 reference & pairwise 距离。
    兼容字段: avg_distance_to_reference, avg_candidate_distance, uncertainty_score.
    返回: document_id, reference_distance, pairwise_distance
    如果 embedding 方法不止一个，优先 E5 其次 Qwen；缺失时尽量回退任意 embedding 行。
    """
    emb_df = df[df['uq_method'].str.contains('Embedding', case=False, na=False)].copy()
    if emb_df.empty:
        raise ValueError('CSV 中未找到 Embedding 方法行。')
    # 计算优先级
    def priority(m):
        m_lower = m.lower()
        if 'e5' in m_lower:
            return 0
        if 'qwen' in m_lower:
            return 1
        return 2
    emb_df['__prio'] = emb_df['uq_method'].map(priority)
    emb_df.sort_values(['document_id','__prio'], inplace=True)
    # 去重保留优先行
    keep = emb_df.drop_duplicates(subset=['document_id'], keep='first')
    keep['reference_distance'] = keep['avg_distance_to_reference'].apply(safe_float)
    # pairwise distance 优先 avg_candidate_distance => uncertainty_score => uq_value
    keep['pairwise_distance'] = keep['avg_candidate_distance'].apply(safe_float)
    mask_missing = keep['pairwise_distance'].isna()
    keep.loc[mask_missing, 'pairwise_distance'] = keep.loc[mask_missing, 'uncertainty_score'].apply(safe_float)
    mask_missing2 = keep['pairwise_distance'].isna()
    keep.loc[mask_missing2, 'pairwise_distance'] = keep.loc[mask_missing2, 'uq_value'].apply(safe_float)
    out = keep[['document_id','reference_distance','pairwise_distance']].copy()
    out = out.dropna(subset=['reference_distance','pairwise_distance'])
    return out

def extract_multiple_uq_scores(df: pd.DataFrame, methods: List[str]) -> pd.DataFrame:
    """Pivot 指定 uq_method 列表的分数 (uncertainty_score 或 uq_value)。"""
    subset = df[df['uq_method'].isin(methods)].copy()
    if subset.empty:
        return pd.DataFrame({'document_id':[]})
    # 选值列
    def row_score(r):
        for k in ['uncertainty_score','uq_value']:
            v = r.get(k)
            if not pd.isna(v):
                return v
        return np.nan
    subset['uq_score_extracted'] = subset.apply(row_score, axis=1)
    wide = subset.pivot_table(index='document_id', columns='uq_method', values='uq_score_extracted', aggfunc='first')
    wide.reset_index(inplace=True)
    return wide

 # 不再需要原始 responses 解码逻辑

def linear_fit(x: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
    mask = (~np.isnan(x)) & (~np.isnan(y))
    x2 = x[mask][:, None]
    y2 = y[mask]
    if len(x2) < 2:
        return {"coef": None, "intercept": None, "r2": None, "n": len(x2)}
    if SKLEARN_AVAILABLE:
        model = LinearRegression().fit(x2, y2)
        r2 = model.score(x2, y2)
        return {"coef": float(model.coef_[0]), "intercept": float(model.intercept_), "r2": float(r2), "n": int(len(x2))}
    # fallback
    x_mean, y_mean = x2.mean(), y2.mean()
    num = ((x2 - x_mean) * (y2 - y_mean)).sum()
    den = ((x2 - x_mean)**2).sum()
    if den == 0:
        return {"coef": None, "intercept": None, "r2": None, "n": len(x2)}
    a = num / den
    b = y_mean - a * x_mean
    # r2
    ss_tot = ((y2 - y_mean)**2).sum()
    ss_res = ((y2 - (a * x2 + b))**2).sum()
    r2 = 1 - ss_res/ss_tot if ss_tot > 0 else None
    return {"coef": float(a), "intercept": float(b), "r2": float(r2) if r2 is not None else None, "n": int(len(x2))}


def get_available_uq_methods(df: pd.DataFrame) -> List[str]:
    """获取数据中实际存在的UQ方法，包括embedding方法"""
    all_methods = df['uq_method'].unique()
    return sorted(all_methods)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--task', required=True, choices=sorted(list(REFERENCE_TASKS)))
    parser.add_argument('--uq-methods', default='', help='逗号分隔的UQ方法名称，留空则使用所有可用方法')
    parser.add_argument('--limit', type=int, default=0, help='仅使用前N个document (按 CSV 出现顺序)')
    args = parser.parse_args()

    df = load_task_dataframe(args.task)
    emb_base = extract_embedding_distances(df)
    if args.limit > 0:
        # 限制 document 数量
        keep_ids = emb_base['document_id'].head(args.limit).tolist()
        emb_base = emb_base[emb_base['document_id'].isin(keep_ids)]

    # 获取UQ方法列表
    if args.uq_methods.strip():
        uq_methods = [m.strip() for m in args.uq_methods.split(',') if m.strip()]
    else:
        uq_methods = get_available_uq_methods(df)

    print(f"将为以下UQ方法生成单独的图表: {uq_methods}")

    uq_wide = extract_multiple_uq_scores(df, uq_methods)
    merged = emb_base.merge(uq_wide, on='document_id', how='left')
    merged['task'] = args.task

    # 保存基础汇总 CSV
    base_csv = FIG_DIR / f'embedding_reference_individual_{args.task}.csv'
    merged.to_csv(base_csv, index=False)
    print(f'保存聚合数据到 {base_csv} (rows={len(merged)})')

    # 为每个UQ方法生成单独的图
    fit_results = {}
    try:
        import matplotlib.pyplot as plt

        for method in uq_methods:
            if method not in merged.columns:
                print(f"跳过方法 {method}: 数据中不存在")
                continue

            # 准备数据 - 注意坐标轴倒置
            x = np.array(merged[method].values, dtype=float)  # UQ分数作为X轴
            y = np.array(merged['reference_distance'].values, dtype=float)  # reference距离作为Y轴

            # 过滤NaN值
            mask = (~np.isnan(x)) & (~np.isnan(y))
            if mask.sum() < 2:
                print(f"跳过方法 {method}: 有效数据点不足")
                continue

            x_clean = x[mask]
            y_clean = y[mask]

            # 线性拟合
            fit_result = linear_fit(x_clean, y_clean)
            fit_results[method] = fit_result

            # 创建图表
            plt.figure(figsize=(8, 6))
            plt.scatter(x_clean, y_clean, s=20, alpha=0.6, color='steelblue')

            # 绘制拟合直线和公式
            if fit_result['coef'] is not None:
                x_range = np.linspace(float(np.nanmin(x_clean)), float(np.nanmax(x_clean)), 100)
                y_fit = fit_result['coef'] * x_range + fit_result['intercept']
                plt.plot(x_range, y_fit, 'r-', linewidth=2, alpha=0.8)

                # 显示拟合公式
                coef = fit_result['coef']
                intercept = fit_result['intercept']
                r2 = fit_result['r2']
                n = fit_result['n']

                if intercept >= 0:
                    formula = f'y = {coef:.4f}x + {intercept:.4f}'
                else:
                    formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'

                r2_str = f"R² = {r2:.4f}" if r2 is not None else "R² = -"

                # 在图上显示公式和R²
                plt.text(0.05, 0.95, formula, transform=plt.gca().transAxes,
                        fontsize=12, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                plt.text(0.05, 0.88, r2_str, transform=plt.gca().transAxes,
                        fontsize=12, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
                plt.text(0.05, 0.81, f'n = {n}', transform=plt.gca().transAxes,
                        fontsize=12, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

            plt.xlabel(f'{method} Score', fontsize=12)
            plt.ylabel('Mean Distance (responses vs reference)', fontsize=12)
            plt.title(f'{args.task}: {method} vs Reference Distance', fontsize=14)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            # 保存图像
            png_file = FIG_DIR / f'embedding_reference_{args.task}_{method}.png'
            plt.savefig(png_file, dpi=220, bbox_inches='tight')
            plt.close()
            print(f'保存 {method} 图像到 {png_file}')

    except Exception as e:
        print('绘图失败:', e)
        import traceback
        traceback.print_exc()

    # 保存拟合结果 JSON
    fit_json = FIG_DIR / f'embedding_reference_individual_{args.task}_fits.json'
    with open(fit_json, 'w', encoding='utf-8') as f:
        json.dump(fit_results, f, ensure_ascii=False, indent=2)
    print(f'拟合参数保存到 {fit_json}')
    print(f'共生成 {len(fit_results)} 个图表')

if __name__ == '__main__':
    main()
